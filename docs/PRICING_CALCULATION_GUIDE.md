# Trip Pricing Calculation Guide

## Overview

This document provides a comprehensive guide to how trip pricing is calculated in the ZTS (Zuwara Transportation System). The pricing system applies multiple factors in a specific order to determine the final trip price.

## Final Formula

```
Final Price = (B + D × distance) × (1 + Overcharge)
```

Where:
- **B** = Adjusted Base Fare (after all adjustments)
- **D** = Adjusted Distance Fare per KM (after all adjustments)
- **distance** = Trip distance in kilometers
- **Overcharge** = Time overcharge factor (if applicable)

## Calculation Order

The pricing factors are applied in this **exact order** (as implemented in `PricingService.php`):

### 1. Global Rules (Starting Point)
- **Base Fare (B)** = Global base price setting
- **Distance Rate (D)** = Global price per kilometer setting
- **Time Threshold** = Global time threshold percentage

### 2. Area Adjustment (First)
- Only applies if departure area is active (`is_active = true`)
- Only applies if area has pricing configuration
- **Fixed**: Uses area's `base_fare` and `distance_fare` values directly
- **Percentage**: Calculates `original × (adjustment / 100)` and adds to original

### 3. Seat Capacity Adjustment (Second)
- Supports exactly 2, 4, or 6 seat vehicles only
- **Fixed**: Uses seat rule's `base_fare` and `distance_fare` values
- **Percentage**: Calculates `original × (adjustment / 100)` and adds to original

### 4. Vehicle Type Adjustment (Third)
- Applied based on selected vehicle category (Economy, Comfort, Luxury, etc.)
- **Fixed**: Uses vehicle type's `additional_base_fare` and `additional_price_per_km`
- **Percentage**: Calculates `original × (adjustment / 100)` and adds to original

### 5. Gender Adjustment (Fourth)
- Based on rider's **driver preference**, NOT rider's gender
- Only applies when rider specifically requests female driver
- **Fixed**: Uses gender rule's `base_fare` and `distance_fare` values
- **Percentage**: Calculates `original × (adjustment / 100)` and adds to original

### 6. Time/Day Adjustment (Fifth)
- **Day**: Applied during daytime hours
- **Night**: Applied during nighttime hours
- **Fixed**: Uses time rule's day/night fixed charges
- **Percentage**: Calculates `original × (percentage / 100)` and adds to original

### 7. Peak Hour Adjustment (Sixth)
- Applied during configured peak hours
- **Stacks with day/night adjustment**
- **Fixed**: Uses peak rule's fixed charges
- **Percentage**: Calculates `original × (percentage / 100)` and adds to original

### 8. Equipment Charges (Seventh)
- Fixed additional charges for special equipment (child seats, wheelchair access, etc.)
- Added directly to base fare: `B += sum(equipment.additional_fare)`
- Applied **after** all percentage adjustments

### 9. Time Overcharge (Final)
- Only applies to completed trips
- Formula: `TimeDiff = (ActualTime - EstimatedTime) / EstimatedTime`
- Overcharge: `Max(0, TimeDiff - Threshold)`
- Applied as multiplier: `(B + D × distance) × (1 + Overcharge)`

## Adjustment Types

### Fixed Adjustment
- Replaces the original value completely
- Example: Global base $5, Area fixed $7 → Use $7

### Percentage Adjustment
- Adds percentage of **original global value** to current value
- Formula: `adjustment = original_global × (percentage / 100)`
- Example: Global base $5, Area +20% → $5 + ($5 × 0.20) = $6

## Verified Example

**Test Configuration:**
- Global: $5 base + $2/km, 20% time threshold
- Distance: 10 km
- Area: +$1 base (fixed), +10% distance (percentage)
- Seats: 4 seats, +50% base (percentage), +$0.50/km (fixed)
- Vehicle: Luxury, +$2 base (fixed), +25% distance (percentage)
- Gender: Female driver requested
- Time: Night, +15% base, +10% distance (both percentage)
- Peak: Rush hour, +20% base, +15% distance (both percentage)
- Equipment: Child seat +$2 (fixed)
- Overcharge: Trip took 50% longer than estimated, threshold 20%

**Actual System Result:**
- Base Fare: $5
- Distance Rate: $2/km
- Distance: 10km
- Subtotal: $51.25 (after all adjustments)
- Final Price: $62.00 (with time overcharge applied)

✅ **This calculation has been verified by automated tests**

## Testing

The pricing calculation system is thoroughly tested with:

1. **PricingCalculationDocumentationTest** - Verifies the complete pricing flow
2. **Minimal factors test** - Verifies base pricing without adjustments
3. **Order verification test** - Ensures all factors are applied in correct order

Run tests with:
```bash
php artisan test tests/Feature/PricingCalculationDocumentationTest.php
```

## Key Points for QA Testing

1. **Order Matters**: Adjustments are applied in the exact sequence documented
2. **Area Priority**: Area pricing only applies if departure area is marked as "active"
3. **Equipment Timing**: Equipment charges are added after all percentage adjustments
4. **Time Overcharge**: Only applies to completed trips that exceeded time estimates
5. **Gender Logic**: Based on rider's driver preference, not rider's gender
6. **Percentage Base**: All percentage adjustments are calculated from original global values

## Troubleshooting Different Prices

If riders are getting different prices for similar trips, check:
- ✅ Departure area settings and active status
- ✅ Vehicle type configurations
- ✅ Gender preference settings
- ✅ Time-based pricing rules
- ✅ Equipment selections
- ✅ Time overcharge thresholds

Each factor is calculated independently and then combined, so small differences in any setting can create noticeable price variations.
